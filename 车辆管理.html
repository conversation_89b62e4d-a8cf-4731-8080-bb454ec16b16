<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>车辆管理</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
            background-color: #f2f2f7;
            color: #1c1c1e;
            overflow-x: hidden;
        }

        .app-container {
            max-width: 414px;
            margin: 0 auto;
            background: #fff;
            min-height: 100vh;
            position: relative;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }

        /* 状态栏 */
        .status-bar {
            height: 44px;
            background: #fff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
        }

        /* 导航栏 */
        .nav-bar {
            height: 44px;
            background: #fff;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            border-bottom: 0.5px solid #e5e5ea;
        }

        .nav-title {
            font-size: 17px;
            font-weight: 600;
            color: #1c1c1e;
        }

        .nav-button {
            background: none;
            border: none;
            color: #007aff;
            font-size: 17px;
            cursor: pointer;
            padding: 8px;
        }

        /* 主内容区域 */
        .main-content {
            position: relative;
            height: calc(100vh - 171px);
            /* 总高度减去状态栏、导航栏和底部tab栏 */
            overflow: hidden;
        }

        /* 页面容器 */
        .page {
            display: none;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: #f2f2f7;
            z-index: 1;
            overflow-y: auto;
            padding-bottom: 20px;
        }

        .page.active {
            display: block;
            z-index: 2;
        }

        /* 前进动画（push） */
        .page.slide-in-right {
            animation: slideInRight 0.3s ease-out forwards;
        }

        .page.slide-out-left {
            animation: slideOutLeft 0.3s ease-out forwards;
        }

        /* 后退动画（pop） */
        .page.slide-in-left {
            animation: slideInLeft 0.3s ease-out forwards;
        }

        .page.slide-out-right {
            animation: slideOutRight 0.3s ease-out forwards;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
            }

            to {
                transform: translateX(0);
            }
        }

        @keyframes slideOutLeft {
            from {
                transform: translateX(0);
            }

            to {
                transform: translateX(-100%);
            }
        }

        @keyframes slideInLeft {
            from {
                transform: translateX(-100%);
            }

            to {
                transform: translateX(0);
            }
        }

        @keyframes slideOutRight {
            from {
                transform: translateX(0);
            }

            to {
                transform: translateX(100%);
            }
        }

        /* 列表样式 */
        .list-section {
            background: #fff;
            margin: 8px 0;
            border-radius: 10px;
            overflow: hidden;
        }

        .list-item {
            padding: 12px 16px;
            border-bottom: 0.5px solid #e5e5ea;
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .list-item:last-child {
            border-bottom: none;
        }

        .list-item:active {
            background-color: #e5e5ea;
        }

        .list-item-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 16px;
            color: #fff;
        }

        .list-item-content {
            flex: 1;
        }

        .list-item-title {
            font-size: 16px;
            font-weight: 400;
            margin-bottom: 2px;
        }

        .list-item-subtitle {
            font-size: 14px;
            color: #8e8e93;
        }

        .list-item-arrow {
            color: #c7c7cc;
            font-size: 14px;
        }

        /* 卡片样式 */
        .card {
            background: #fff;
            border-radius: 10px;
            margin: 8px 16px;
            padding: 16px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
        }

        .card-subtitle {
            font-size: 14px;
            color: #8e8e93;
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            margin: 16px;
        }

        .stat-card {
            background: #fff;
            border-radius: 10px;
            padding: 16px;
            text-align: center;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .stat-value {
            font-size: 24px;
            font-weight: 600;
            color: #007aff;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 14px;
            color: #8e8e93;
        }

        /* 底部标签栏 */
        .tab-bar {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 414px;
            max-width: 100%;
            height: 83px;
            background: #f8f8f8;
            border-top: 0.5px solid #e5e5ea;
            display: flex;
            padding-bottom: 34px;
            /* iPhone安全区域 */
        }

        .tab-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            padding: 8px 0;
            transition: color 0.2s;
        }

        .tab-item.active {
            color: #007aff;
        }

        .tab-item:not(.active) {
            color: #8e8e93;
        }

        .tab-icon {
            font-size: 24px;
            margin-bottom: 4px;
        }

        .tab-label {
            font-size: 10px;
            font-weight: 500;
        }

        /* 按钮样式 */
        .btn {
            background: #007aff;
            color: #fff;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .btn:active {
            background: #0056b3;
        }

        .btn-secondary {
            background: #e5e5ea;
            color: #1c1c1e;
        }

        .btn-secondary:active {
            background: #d1d1d6;
        }

        /* 输入框样式 */
        .input-group {
            margin: 8px 16px;
        }

        .input-label {
            font-size: 14px;
            color: #8e8e93;
            margin-bottom: 4px;
        }

        .input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #e5e5ea;
            border-radius: 8px;
            font-size: 16px;
            background: #fff;
        }

        .input:focus {
            outline: none;
            border-color: #007aff;
        }

        /* 地图容器 */
        .map-container {
            height: 200px;
            background: #e5e5ea;
            border-radius: 10px;
            margin: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #8e8e93;
        }

        /* 颜色主题 */
        .color-blue {
            background: #007aff;
        }

        .color-green {
            background: #34c759;
        }

        .color-orange {
            background: #ff9500;
        }

        .color-red {
            background: #ff3b30;
        }

        .color-purple {
            background: #af52de;
        }

        .color-gray {
            background: #8e8e93;
        }

        /* 响应式 */
        @media (max-width: 414px) {
            .app-container {
                max-width: 100%;
            }

            .tab-bar {
                width: 100%;
            }
        }

        /* 搜索栏 */
        .search-bar {
            padding: 8px 16px;
            background: #fff;
        }

        .search-input {
            width: 100%;
            padding: 10px 16px;
            background: #f2f2f7;
            border: none;
            border-radius: 10px;
            font-size: 16px;
        }

        .search-input:focus {
            outline: none;
            background: #e5e5ea;
        }

        /* 标签 */
        .badge {
            display: inline-block;
            padding: 2px 8px;
            background: #ff3b30;
            color: #fff;
            border-radius: 10px;
            font-size: 12px;
            font-weight: 600;
            margin-left: 8px;
        }

        .badge.green {
            background: #34c759;
        }

        .badge.orange {
            background: #ff9500;
        }

        .badge.blue {
            background: #007aff;
        }

        /* 开关样式 */
        .switch {
            position: relative;
            display: inline-block;
            width: 51px;
            height: 31px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 31px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 25px;
            width: 25px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked+.slider {
            background-color: #34c759;
        }

        input:checked+.slider:before {
            transform: translateX(20px);
        }
    </style>
</head>

<body>
    <div class="app-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <span>9:41</span>
            <span>车辆管理</span>
            <span>📶 📶 🔋</span>
        </div>

        <!-- 导航栏 -->
        <div class="nav-bar">
            <button class="nav-button" onclick="goBack()" id="backBtn" style="display: none;">
                <i class="fas fa-chevron-left"></i>
            </button>
            <div class="nav-title" id="navTitle">车辆</div>
            <button class="nav-button" onclick="showAddMenu()" id="addBtn">
                <i class="fas fa-plus"></i>
            </button>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">

            <!-- 车辆页面 -->
            <div class="page active" id="vehiclePage">
                <!-- 搜索栏 -->
                <div class="search-bar">
                    <input type="text" class="search-input" placeholder="搜索车辆">
                </div>

                <!-- 快捷功能 -->
                <div class="stats-grid">
                    <div class="stat-card" onclick="showPage('locationPage')">
                        <div class="stat-value"><i class="fas fa-map-marker-alt"></i></div>
                        <div class="stat-label">定位</div>
                    </div>
                    <div class="stat-card" onclick="showPage('fuelPricePage')">
                        <div class="stat-value"><i class="fas fa-gas-pump"></i></div>
                        <div class="stat-label">油价</div>
                    </div>
                    <div class="stat-card" onclick="showPage('weatherPage')">
                        <div class="stat-value"><i class="fas fa-cloud-sun"></i></div>
                        <div class="stat-label">天气</div>
                    </div>
                    <div class="stat-card" onclick="showPage('addressBookPage')">
                        <div class="stat-value"><i class="fas fa-address-book"></i></div>
                        <div class="stat-label">地址本</div>
                    </div>
                </div>

                <!-- 车辆列表 -->
                <div class="list-section">
                    <div class="list-item" onclick="showVehicleDetail('1')">
                        <div class="list-item-icon color-blue">
                            <i class="fas fa-car"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">奔驰E300L</div>
                            <div class="list-item-subtitle">京A12345 • 在线</div>
                        </div>
                        <div class="list-item-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                    <div class="list-item" onclick="showVehicleDetail('2')">
                        <div class="list-item-icon color-green">
                            <i class="fas fa-car"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">比亚迪汉EV</div>
                            <div class="list-item-subtitle">京B67890 • 离线</div>
                        </div>
                        <div class="list-item-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                </div>

                <!-- 提醒 -->
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">提醒事项</div>
                        <span class="badge">2</span>
                    </div>
                    <div class="list-item" style="padding: 8px 0;">
                        <div class="list-item-icon color-orange">
                            <i class="fas fa-wrench"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">保养提醒</div>
                            <div class="list-item-subtitle">奔驰E300L 还有1000km保养</div>
                        </div>
                    </div>
                    <div class="list-item" style="padding: 8px 0;">
                        <div class="list-item-icon color-red">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">保险到期</div>
                            <div class="list-item-subtitle">比亚迪汉EV 保险将在30天后到期</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 车辆详情页面 -->
            <div class="page" id="vehicleDetailPage">
                <div class="card">
                    <div class="card-header">
                        <div class="card-title" id="vehicleDetailTitle">奔驰E300L</div>
                        <span class="badge green">在线</span>
                    </div>
                    <div class="list-item-subtitle">京A12345</div>
                </div>

                <!-- 地图显示 -->
                <div class="map-container">
                    <div>
                        <i class="fas fa-map-marker-alt" style="font-size: 24px; margin-bottom: 8px;"></i>
                        <div>当前位置：北京市朝阳区</div>
                    </div>
                </div>

                <!-- 功能菜单 -->
                <div class="list-section">
                    <div class="list-item" onclick="showPage('trackPage')">
                        <div class="list-item-icon color-blue">
                            <i class="fas fa-route"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">行车轨迹</div>
                        </div>
                        <div class="list-item-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                    <div class="list-item" onclick="showPage('fenceManagePage')">
                        <div class="list-item-icon color-purple">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">电子围栏</div>
                        </div>
                        <div class="list-item-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                    <div class="list-item" onclick="showPage('expenseRecordPage')">
                        <div class="list-item-icon color-green">
                            <i class="fas fa-coins"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">费用记录</div>
                        </div>
                        <div class="list-item-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 故事页面 -->
            <div class="page" id="storyPage">
                <div class="search-bar">
                    <input type="text" class="search-input" placeholder="搜索故事记录">
                </div>

                <!-- 今日故事 -->
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">今日故事</div>
                    </div>
                    <div class="list-item" onclick="showStoryDetail('1')">
                        <div class="list-item-icon color-green">
                            <i class="fas fa-gas-pump"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">加油记录</div>
                            <div class="list-item-subtitle">加92号汽油50L，花费350元</div>
                        </div>
                        <div class="list-item-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                </div>

                <!-- 故事列表 -->
                <div class="list-section">
                    <div class="list-item" onclick="showStoryDetail('2')">
                        <div class="list-item-icon color-orange">
                            <i class="fas fa-wrench"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">保养记录</div>
                            <div class="list-item-subtitle">昨天 • 花费1200元</div>
                        </div>
                        <div class="list-item-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                    <div class="list-item" onclick="showStoryDetail('3')">
                        <div class="list-item-icon color-red">
                            <i class="fas fa-tools"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">维修记录</div>
                            <div class="list-item-subtitle">3天前 • 花费800元</div>
                        </div>
                        <div class="list-item-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                    <div class="list-item" onclick="showStoryDetail('4')">
                        <div class="list-item-icon color-blue">
                            <i class="fas fa-charging-station"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">充电记录</div>
                            <div class="list-item-subtitle">一周前 • 充电费用45元</div>
                        </div>
                        <div class="list-item-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 行程页面 -->
            <div class="page" id="tripPage">
                <!-- 行程统计 -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value">1,234</div>
                        <div class="stat-label">总里程(km)</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">45</div>
                        <div class="stat-label">总行程</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">8.5</div>
                        <div class="stat-label">平均油耗(L)</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">¥2,340</div>
                        <div class="stat-label">总费用</div>
                    </div>
                </div>

                <!-- 行程列表 -->
                <div class="list-section">
                    <div class="list-item" onclick="showTripDetail('1')">
                        <div class="list-item-icon color-blue">
                            <i class="fas fa-route"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">北京 → 天津</div>
                            <div class="list-item-subtitle">今天 14:30 • 125km • 1小时20分</div>
                        </div>
                        <div class="list-item-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                    <div class="list-item" onclick="showTripDetail('2')">
                        <div class="list-item-icon color-green">
                            <i class="fas fa-route"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">家 → 公司</div>
                            <div class="list-item-subtitle">今天 09:00 • 25km • 45分钟</div>
                        </div>
                        <div class="list-item-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 我的页面 -->
            <div class="page" id="minePage">
                <!-- 用户信息 -->
                <div class="card">
                    <div style="display: flex; align-items: center;">
                        <div
                            style="width: 60px; height: 60px; background: #007aff; border-radius: 30px; display: flex; align-items: center; justify-content: center; color: white; font-size: 24px; margin-right: 16px;">
                            <i class="fas fa-user"></i>
                        </div>
                        <div>
                            <div style="font-size: 18px; font-weight: 600; margin-bottom: 4px;">车主</div>
                            <div style="font-size: 14px; color: #8e8e93;">138****1234</div>
                        </div>
                    </div>
                </div>

                <!-- 商店 -->
                <div class="list-section">
                    <div class="list-item" onclick="showPage('shopPage')">
                        <div class="list-item-icon color-orange">
                            <i class="fas fa-store"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">商店</div>
                            <div class="list-item-subtitle">购买定位设备</div>
                        </div>
                        <div class="list-item-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                    <div class="list-item" onclick="showPage('orderPage')">
                        <div class="list-item-icon color-blue">
                            <i class="fas fa-shopping-bag"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">我的订单</div>
                        </div>
                        <div class="list-item-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                </div>

                <!-- 统计 -->
                <div class="list-section">
                    <div class="list-item" onclick="showPage('tripStatsPage')">
                        <div class="list-item-icon color-purple">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">行程统计</div>
                        </div>
                        <div class="list-item-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                    <div class="list-item" onclick="showPage('financeStatsPage')">
                        <div class="list-item-icon color-green">
                            <i class="fas fa-chart-pie"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">收支统计</div>
                        </div>
                        <div class="list-item-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                    <div class="list-item" onclick="showPage('energyStatsPage')">
                        <div class="list-item-icon color-red">
                            <i class="fas fa-battery-half"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">能耗统计</div>
                        </div>
                        <div class="list-item-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                </div>

                <!-- 设置 -->
                <div class="list-section">
                    <div class="list-item" onclick="showPage('reminderPage')">
                        <div class="list-item-icon color-orange">
                            <i class="fas fa-bell"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">提醒设置</div>
                        </div>
                        <div class="list-item-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                    <div class="list-item">
                        <div class="list-item-icon color-gray">
                            <i class="fas fa-cog"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">设置</div>
                        </div>
                        <div class="list-item-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 定位页面 -->
            <div class="page" id="locationPage">
                <div class="map-container" style="height: 300px;">
                    <div>
                        <i class="fas fa-map-marker-alt"
                            style="font-size: 32px; color: #007aff; margin-bottom: 16px;"></i>
                        <div style="text-align: center;">
                            <div style="font-size: 16px; font-weight: 600; margin-bottom: 8px;">车辆定位</div>
                            <div style="font-size: 14px;">北京市朝阳区建国门外大街</div>
                            <div style="font-size: 12px; color: #8e8e93; margin-top: 4px;">更新时间: 2分钟前</div>
                        </div>
                    </div>
                </div>

                <div class="list-section">
                    <div class="list-item">
                        <div class="list-item-icon color-blue">
                            <i class="fas fa-crosshairs"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">精确定位</div>
                            <div class="list-item-subtitle">39.9042°N, 116.4074°E</div>
                        </div>
                    </div>
                    <div class="list-item">
                        <div class="list-item-icon color-green">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">当前速度</div>
                            <div class="list-item-subtitle">45 km/h</div>
                        </div>
                    </div>
                    <div class="list-item">
                        <div class="list-item-icon color-orange">
                            <i class="fas fa-compass"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">行驶方向</div>
                            <div class="list-item-subtitle">东北方向</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 油价查询页面 -->
            <div class="page" id="fuelPricePage">
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">今日油价</div>
                        <div class="card-subtitle">北京地区</div>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value">¥7.85</div>
                        <div class="stat-label">92号汽油</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">¥8.36</div>
                        <div class="stat-label">95号汽油</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">¥8.86</div>
                        <div class="stat-label">98号汽油</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">¥7.45</div>
                        <div class="stat-label">0号柴油</div>
                    </div>
                </div>

                <div class="list-section">
                    <div class="list-item">
                        <div class="list-item-icon color-green">
                            <i class="fas fa-gas-pump"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">中石化 建国门加油站</div>
                            <div class="list-item-subtitle">距离 1.2km • 92号 ¥7.83</div>
                        </div>
                        <div class="list-item-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                    <div class="list-item">
                        <div class="list-item-icon color-blue">
                            <i class="fas fa-gas-pump"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">中石油 朝阳门加油站</div>
                            <div class="list-item-subtitle">距离 2.1km • 92号 ¥7.85</div>
                        </div>
                        <div class="list-item-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 天气页面 -->
            <div class="page" id="weatherPage">
                <div class="card">
                    <div style="text-align: center; padding: 20px 0;">
                        <div style="font-size: 48px; margin-bottom: 16px;">☀️</div>
                        <div style="font-size: 32px; font-weight: 600; margin-bottom: 8px;">25°C</div>
                        <div style="font-size: 16px; color: #8e8e93; margin-bottom: 4px;">晴朗</div>
                        <div style="font-size: 14px; color: #8e8e93;">北京市朝阳区</div>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value">15°</div>
                        <div class="stat-label">最低温度</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">28°</div>
                        <div class="stat-label">最高温度</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">65%</div>
                        <div class="stat-label">湿度</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">15km/h</div>
                        <div class="stat-label">风速</div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-title" style="margin-bottom: 16px;">7天预报</div>
                    <div class="list-item" style="padding: 8px 0;">
                        <div>今天</div>
                        <div style="flex: 1; text-align: center;">☀️</div>
                        <div>15° / 28°</div>
                    </div>
                    <div class="list-item" style="padding: 8px 0;">
                        <div>明天</div>
                        <div style="flex: 1; text-align: center;">⛅</div>
                        <div>18° / 26°</div>
                    </div>
                    <div class="list-item" style="padding: 8px 0;">
                        <div>后天</div>
                        <div style="flex: 1; text-align: center;">🌧️</div>
                        <div>12° / 22°</div>
                    </div>
                </div>
            </div>

            <!-- 地址本页面 -->
            <div class="page" id="addressBookPage">
                <div class="search-bar">
                    <input type="text" class="search-input" placeholder="搜索地址">
                </div>

                <div class="list-section">
                    <div class="list-item">
                        <div class="list-item-icon color-blue">
                            <i class="fas fa-home"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">家</div>
                            <div class="list-item-subtitle">北京市朝阳区望京街道</div>
                        </div>
                        <div class="list-item-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                    <div class="list-item">
                        <div class="list-item-icon color-green">
                            <i class="fas fa-building"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">公司</div>
                            <div class="list-item-subtitle">北京市海淀区中关村</div>
                        </div>
                        <div class="list-item-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                    <div class="list-item">
                        <div class="list-item-icon color-orange">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">常去的餐厅</div>
                            <div class="list-item-subtitle">北京市东城区王府井大街</div>
                        </div>
                        <div class="list-item-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 行车轨迹页面 -->
            <div class="page" id="trackPage">
                <div class="map-container" style="height: 250px;">
                    <div>
                        <i class="fas fa-route" style="font-size: 24px; margin-bottom: 8px;"></i>
                        <div>轨迹回放</div>
                        <div style="font-size: 12px; color: #8e8e93; margin-top: 4px;">显示今日行车路线</div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <div class="card-title">轨迹信息</div>
                    </div>
                    <div class="stats-grid" style="margin: 0; gap: 16px;">
                        <div style="text-align: center;">
                            <div style="font-size: 20px; font-weight: 600; color: #007aff;">125.6km</div>
                            <div style="font-size: 12px; color: #8e8e93;">总里程</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 20px; font-weight: 600; color: #34c759;">2小时15分</div>
                            <div style="font-size: 12px; color: #8e8e93;">行驶时长</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 20px; font-weight: 600; color: #ff9500;">56km/h</div>
                            <div style="font-size: 12px; color: #8e8e93;">平均速度</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 20px; font-weight: 600; color: #ff3b30;">8.5L</div>
                            <div style="font-size: 12px; color: #8e8e93;">油耗</div>
                        </div>
                    </div>
                </div>

                <div class="list-section">
                    <div class="list-item">
                        <div class="list-item-content">
                            <div class="list-item-title">09:15 出发</div>
                            <div class="list-item-subtitle">北京市朝阳区望京街道</div>
                        </div>
                    </div>
                    <div class="list-item">
                        <div class="list-item-content">
                            <div class="list-item-title">11:30 到达</div>
                            <div class="list-item-subtitle">天津市和平区南京路</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 电子围栏页面 -->
            <div class="page" id="fenceManagePage">
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">围栏状态</div>
                        <span class="badge green">3个活跃</span>
                    </div>
                </div>

                <div class="list-section">
                    <div class="list-item" onclick="showPage('fenceDetailPage')">
                        <div class="list-item-icon color-green">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">家庭区域</div>
                            <div class="list-item-subtitle">半径500m • 启用</div>
                        </div>
                        <div class="list-item-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                    <div class="list-item" onclick="showPage('fenceDetailPage')">
                        <div class="list-item-icon color-blue">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">工作区域</div>
                            <div class="list-item-subtitle">半径800m • 启用</div>
                        </div>
                        <div class="list-item-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                    <div class="list-item" onclick="showPage('fenceDetailPage')">
                        <div class="list-item-icon color-orange">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">限制区域</div>
                            <div class="list-item-subtitle">半径1000m • 启用</div>
                        </div>
                        <div class="list-item-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <div class="card-title">围栏记录</div>
                    </div>
                    <div class="list-item" style="padding: 8px 0;">
                        <div class="list-item-icon color-red">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">离开工作区域</div>
                            <div class="list-item-subtitle">2小时前 • 奔驰E300L</div>
                        </div>
                    </div>
                    <div class="list-item" style="padding: 8px 0;">
                        <div class="list-item-icon color-green">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">进入家庭区域</div>
                            <div class="list-item-subtitle">昨天 18:30 • 奔驰E300L</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 费用记录页面 -->
            <div class="page" id="expenseRecordPage">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value">¥2,340</div>
                        <div class="stat-label">本月支出</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">¥850</div>
                        <div class="stat-label">本月收入</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">¥1,490</div>
                        <div class="stat-label">净支出</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">15</div>
                        <div class="stat-label">记录条数</div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <div class="card-title">支出分类</div>
                    </div>
                    <div class="list-item" style="padding: 8px 0;">
                        <div class="list-item-icon color-green">
                            <i class="fas fa-gas-pump"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">燃油费用</div>
                            <div class="list-item-subtitle">¥980</div>
                        </div>
                    </div>
                    <div class="list-item" style="padding: 8px 0;">
                        <div class="list-item-icon color-orange">
                            <i class="fas fa-wrench"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">保养费用</div>
                            <div class="list-item-subtitle">¥1,200</div>
                        </div>
                    </div>
                    <div class="list-item" style="padding: 8px 0;">
                        <div class="list-item-icon color-red">
                            <i class="fas fa-tools"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">维修费用</div>
                            <div class="list-item-subtitle">¥160</div>
                        </div>
                    </div>
                </div>

                <div class="list-section">
                    <div class="list-item">
                        <div class="list-item-icon color-green">
                            <i class="fas fa-plus"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">滴滴收入</div>
                            <div class="list-item-subtitle">今天 • +¥125</div>
                        </div>
                        <div style="color: #34c759; font-weight: 600;">+¥125</div>
                    </div>
                    <div class="list-item">
                        <div class="list-item-icon color-blue">
                            <i class="fas fa-gas-pump"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">加油</div>
                            <div class="list-item-subtitle">今天 • 92号汽油 50L</div>
                        </div>
                        <div style="color: #ff3b30; font-weight: 600;">-¥350</div>
                    </div>
                    <div class="list-item">
                        <div class="list-item-icon color-orange">
                            <i class="fas fa-wrench"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">保养</div>
                            <div class="list-item-subtitle">昨天 • 更换机油机滤</div>
                        </div>
                        <div style="color: #ff3b30; font-weight: 600;">-¥1,200</div>
                    </div>
                </div>
            </div>

            <!-- 商店页面 -->
            <div class="page" id="shopPage">
                <div class="search-bar">
                    <input type="text" class="search-input" placeholder="搜索商品">
                </div>

                <div class="card">
                    <div class="card-header">
                        <div class="card-title">推荐商品</div>
                    </div>
                </div>

                <div class="list-section">
                    <div class="list-item">
                        <div class="list-item-icon color-blue">
                            <i class="fas fa-satellite-dish"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">4G车载定位器</div>
                            <div class="list-item-subtitle">支持实时定位，超长待机</div>
                        </div>
                        <div style="color: #ff3b30; font-weight: 600;">¥299</div>
                    </div>
                    <div class="list-item">
                        <div class="list-item-icon color-green">
                            <i class="fas fa-wifi"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">无线OBD诊断器</div>
                            <div class="list-item-subtitle">实时监测车辆状态</div>
                        </div>
                        <div style="color: #ff3b30; font-weight: 600;">¥199</div>
                    </div>
                    <div class="list-item">
                        <div class="list-item-icon color-orange">
                            <i class="fas fa-camera"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">行车记录仪</div>
                            <div class="list-item-subtitle">高清夜视，循环录像</div>
                        </div>
                        <div style="color: #ff3b30; font-weight: 600;">¥399</div>
                    </div>
                </div>
            </div>

            <!-- 我的订单页面 -->
            <div class="page" id="orderPage">
                <div class="list-section">
                    <div class="list-item">
                        <div class="list-item-icon color-orange">
                            <i class="fas fa-satellite-dish"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">4G车载定位器</div>
                            <div class="list-item-subtitle">2024-01-15 • 配送中</div>
                        </div>
                        <span class="badge orange">配送中</span>
                    </div>
                    <div class="list-item">
                        <div class="list-item-icon color-green">
                            <i class="fas fa-camera"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">行车记录仪</div>
                            <div class="list-item-subtitle">2024-01-10 • 已完成</div>
                        </div>
                        <span class="badge green">已完成</span>
                    </div>
                </div>
            </div>

            <!-- 提醒设置页面 -->
            <div class="page" id="reminderPage">
                <div class="list-section">
                    <div class="list-item">
                        <div class="list-item-icon color-orange">
                            <i class="fas fa-wrench"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">保养提醒</div>
                            <div class="list-item-subtitle">每5000km提醒一次</div>
                        </div>
                        <label class="switch">
                            <input type="checkbox" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                    <div class="list-item">
                        <div class="list-item-icon color-red">
                            <i class="fas fa-certificate"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">保险到期提醒</div>
                            <div class="list-item-subtitle">提前30天提醒</div>
                        </div>
                        <label class="switch">
                            <input type="checkbox" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                    <div class="list-item">
                        <div class="list-item-icon color-blue">
                            <i class="fas fa-gas-pump"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">低油量提醒</div>
                            <div class="list-item-subtitle">油量低于1/4时提醒</div>
                        </div>
                        <label class="switch">
                            <input type="checkbox" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>
            </div>

            <!-- 行程统计页面 -->
            <div class="page" id="tripStatsPage">
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">本月统计</div>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value">1,234</div>
                        <div class="stat-label">总里程(km)</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">45</div>
                        <div class="stat-label">总行程</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">27.3</div>
                        <div class="stat-label">平均速度(km/h)</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">8.5</div>
                        <div class="stat-label">平均油耗(L/100km)</div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-title" style="margin-bottom: 16px;">月度趋势</div>
                    <div
                        style="height: 200px; background: #f2f2f7; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #8e8e93;">
                        <div>
                            <i class="fas fa-chart-line" style="font-size: 24px; margin-bottom: 8px;"></i>
                            <div>里程趋势图</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 收支统计页面 -->
            <div class="page" id="financeStatsPage">
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">本月收支</div>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value" style="color: #ff3b30;">¥2,340</div>
                        <div class="stat-label">总支出</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" style="color: #34c759;">¥850</div>
                        <div class="stat-label">总收入</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" style="color: #ff9500;">¥1,490</div>
                        <div class="stat-label">净支出</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">¥1.89</div>
                        <div class="stat-label">每公里成本</div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-title" style="margin-bottom: 16px;">支出分布</div>
                    <div
                        style="height: 200px; background: #f2f2f7; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #8e8e93;">
                        <div>
                            <i class="fas fa-chart-pie" style="font-size: 24px; margin-bottom: 8px;"></i>
                            <div>支出分布饼图</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 能耗统计页面 -->
            <div class="page" id="energyStatsPage">
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">本月能耗</div>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value">8.5L</div>
                        <div class="stat-label">平均油耗</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">105L</div>
                        <div class="stat-label">总油耗</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">¥735</div>
                        <div class="stat-label">燃油费用</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">2.1kg</div>
                        <div class="stat-label">CO₂排放</div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-title" style="margin-bottom: 16px;">油耗趋势</div>
                    <div
                        style="height: 200px; background: #f2f2f7; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #8e8e93;">
                        <div>
                            <i class="fas fa-battery-half" style="font-size: 24px; margin-bottom: 8px;"></i>
                            <div>油耗趋势图</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 故事详情页面 -->
            <div class="page" id="storyDetailPage">
                <div class="card">
                    <div class="card-header">
                        <div class="card-title" id="storyDetailTitle">加油记录</div>
                        <span class="badge blue">支出</span>
                    </div>
                </div>

                <div class="map-container" style="height: 200px;">
                    <div>
                        <i class="fas fa-gas-pump" style="font-size: 24px; margin-bottom: 8px;"></i>
                        <div>中石化加油站</div>
                        <div style="font-size: 12px; color: #8e8e93; margin-top: 4px;">北京市朝阳区建国门外大街</div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <div class="card-title">详细信息</div>
                    </div>
                    <div class="list-item" style="padding: 8px 0;">
                        <div class="list-item-content">
                            <div class="list-item-title">金额</div>
                            <div class="list-item-subtitle">¥350.00</div>
                        </div>
                    </div>
                    <div class="list-item" style="padding: 8px 0;">
                        <div class="list-item-content">
                            <div class="list-item-title">油品类型</div>
                            <div class="list-item-subtitle">92号汽油</div>
                        </div>
                    </div>
                    <div class="list-item" style="padding: 8px 0;">
                        <div class="list-item-content">
                            <div class="list-item-title">加油量</div>
                            <div class="list-item-subtitle">50.0L</div>
                        </div>
                    </div>
                    <div class="list-item" style="padding: 8px 0;">
                        <div class="list-item-content">
                            <div class="list-item-title">单价</div>
                            <div class="list-item-subtitle">¥7.00/L</div>
                        </div>
                    </div>
                    <div class="list-item" style="padding: 8px 0;">
                        <div class="list-item-content">
                            <div class="list-item-title">时间</div>
                            <div class="list-item-subtitle">2024-01-15 14:30</div>
                        </div>
                    </div>
                    <div class="list-item" style="padding: 8px 0;">
                        <div class="list-item-content">
                            <div class="list-item-title">里程</div>
                            <div class="list-item-subtitle">68,234km</div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <div class="card-title">备注</div>
                    </div>
                    <div style="padding: 8px 0; color: #8e8e93;">
                        长途出行前加满油箱
                    </div>
                </div>
            </div>

            <!-- 行程详情页面 -->
            <div class="page" id="tripDetailPage">
                <div class="card">
                    <div class="card-header">
                        <div class="card-title" id="tripDetailTitle">北京 → 天津</div>
                        <span class="badge green">已完成</span>
                    </div>
                    <div class="list-item-subtitle">2024-01-15 14:30 - 15:50</div>
                </div>

                <div class="map-container" style="height: 300px;">
                    <div>
                        <i class="fas fa-route" style="font-size: 32px; color: #007aff; margin-bottom: 16px;"></i>
                        <div style="text-align: center;">
                            <div style="font-size: 16px; font-weight: 600; margin-bottom: 8px;">行程轨迹</div>
                            <div style="font-size: 14px;">总里程: 125.6km</div>
                            <div style="font-size: 12px; color: #8e8e93; margin-top: 4px;">用时: 1小时20分钟</div>
                        </div>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value">125.6</div>
                        <div class="stat-label">里程(km)</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">80分钟</div>
                        <div class="stat-label">用时</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">94km/h</div>
                        <div class="stat-label">平均速度</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">9.2L</div>
                        <div class="stat-label">油耗</div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <div class="card-title">行程节点</div>
                    </div>
                    <div class="list-item" style="padding: 8px 0;">
                        <div class="list-item-icon color-green">
                            <i class="fas fa-play"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">14:30 出发</div>
                            <div class="list-item-subtitle">北京市朝阳区望京街道</div>
                        </div>
                    </div>
                    <div class="list-item" style="padding: 8px 0;">
                        <div class="list-item-icon color-orange">
                            <i class="fas fa-pause"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">15:15 休息</div>
                            <div class="list-item-subtitle">廊坊服务区 (停留15分钟)</div>
                        </div>
                    </div>
                    <div class="list-item" style="padding: 8px 0;">
                        <div class="list-item-icon color-red">
                            <i class="fas fa-stop"></i>
                        </div>
                        <div class="list-item-content">
                            <div class="list-item-title">15:50 到达</div>
                            <div class="list-item-subtitle">天津市和平区南京路</div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <div class="card-title">费用统计</div>
                    </div>
                    <div class="list-item" style="padding: 8px 0;">
                        <div class="list-item-content">
                            <div class="list-item-title">燃油费用</div>
                            <div class="list-item-subtitle">¥64.40</div>
                        </div>
                    </div>
                    <div class="list-item" style="padding: 8px 0;">
                        <div class="list-item-content">
                            <div class="list-item-title">过路费</div>
                            <div class="list-item-subtitle">¥55.00</div>
                        </div>
                    </div>
                    <div class="list-item" style="padding: 8px 0;">
                        <div class="list-item-content">
                            <div class="list-item-title">总费用</div>
                            <div class="list-item-subtitle">¥119.40</div>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        <!-- 底部标签栏 -->
        <div class="tab-bar">
            <div class="tab-item active" onclick="switchTab('vehicle')">
                <div class="tab-icon">🚗</div>
                <div class="tab-label">车辆</div>
            </div>
            <div class="tab-item" onclick="switchTab('story')">
                <div class="tab-icon">📖</div>
                <div class="tab-label">故事</div>
            </div>
            <div class="tab-item" onclick="switchTab('trip')">
                <div class="tab-icon">🛣️</div>
                <div class="tab-label">行程</div>
            </div>
            <div class="tab-item" onclick="switchTab('mine')">
                <div class="tab-icon">👤</div>
                <div class="tab-label">我的</div>
            </div>
        </div>
    </div>

    <script>
        // 当前页面栈
        let pageStack = ['vehiclePage'];
        let currentTab = 'vehicle';

        // 切换标签
        function switchTab(tab) {
            // 更新标签栏状态
            document.querySelectorAll('.tab-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector(`[onclick="switchTab('${tab}')"]`).classList.add('active');

            // 清空页面栈，显示对应主页面（无动画）
            pageStack = [];
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active', 'slide-in-right', 'slide-out-left', 'slide-in-left', 'slide-out-right');
            });

            currentTab = tab;
            let pageId = tab + 'Page';
            document.getElementById(pageId).classList.add('active');
            pageStack.push(pageId);

            // 更新导航栏
            updateNavBar();
        }

        // 显示页面（前进动画）
        function showPage(pageId) {
            if (pageStack.length === 0) return;

            const currentPageId = pageStack[pageStack.length - 1];
            const currentPage = document.getElementById(currentPageId);
            const newPage = document.getElementById(pageId);

            if (!newPage) return;

            // 清除之前的动画类
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('slide-in-right', 'slide-out-left', 'slide-in-left', 'slide-out-right');
            });

            // 添加到页面栈
            pageStack.push(pageId);

            // 设置新页面初始状态
            newPage.classList.add('active');
            newPage.style.display = 'block';

            // 开始动画
            requestAnimationFrame(() => {
                currentPage.classList.add('slide-out-left');
                newPage.classList.add('slide-in-right');
            });

            // 动画结束后清理
            setTimeout(() => {
                currentPage.classList.remove('active', 'slide-out-left');
                currentPage.style.display = 'none';
                newPage.classList.remove('slide-in-right');
                updateNavBar();
            }, 300);
        }

        // 返回上一页（后退动画）
        function goBack() {
            if (pageStack.length <= 1) return;

            const currentPageId = pageStack[pageStack.length - 1];
            pageStack.pop();
            const previousPageId = pageStack[pageStack.length - 1];

            const currentPage = document.getElementById(currentPageId);
            const previousPage = document.getElementById(previousPageId);

            // 清除之前的动画类
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('slide-in-right', 'slide-out-left', 'slide-in-left', 'slide-out-right');
            });

            // 设置前一页初始状态
            previousPage.classList.add('active');
            previousPage.style.display = 'block';

            // 开始动画
            requestAnimationFrame(() => {
                currentPage.classList.add('slide-out-right');
                previousPage.classList.add('slide-in-left');
            });

            // 动画结束后清理
            setTimeout(() => {
                currentPage.classList.remove('active', 'slide-out-right');
                currentPage.style.display = 'none';
                previousPage.classList.remove('slide-in-left');
                updateNavBar();
            }, 300);
        }

        // 更新导航栏
        function updateNavBar() {
            const currentPageId = pageStack[pageStack.length - 1];
            const backBtn = document.getElementById('backBtn');
            const navTitle = document.getElementById('navTitle');
            const addBtn = document.getElementById('addBtn');

            // 显示/隐藏返回按钮
            if (pageStack.length > 1) {
                backBtn.style.display = 'block';
            } else {
                backBtn.style.display = 'none';
            }

            // 设置标题
            const titles = {
                'vehiclePage': '车辆',
                'storyPage': '故事',
                'tripPage': '行程',
                'minePage': '我的',
                'vehicleDetailPage': '车辆详情',
                'locationPage': '定位',
                'fuelPricePage': '油价查询',
                'weatherPage': '天气',
                'addressBookPage': '地址本',
                'trackPage': '行车轨迹',
                'fenceManagePage': '电子围栏',
                'expenseRecordPage': '费用记录',
                'shopPage': '商店',
                'orderPage': '我的订单',
                'reminderPage': '提醒设置',
                'tripStatsPage': '行程统计',
                'financeStatsPage': '收支统计',
                'energyStatsPage': '能耗统计',
                'fenceDetailPage': '围栏详情',
                'storyDetailPage': '故事详情',
                'tripDetailPage': '行程详情'
            };
            navTitle.textContent = titles[currentPageId] || '车辆管理';

            // 控制添加按钮显示
            if (['vehiclePage', 'storyPage', 'tripPage'].includes(currentPageId)) {
                addBtn.style.display = 'block';
            } else {
                addBtn.style.display = 'none';
            }
        }

        // 显示车辆详情
        function showVehicleDetail(vehicleId) {
            showPage('vehicleDetailPage');
            // 这里可以根据vehicleId加载具体车辆数据
        }

        // 显示故事详情
        function showStoryDetail(storyId) {
            showPage('storyDetailPage');
            // 这里可以根据storyId加载具体故事数据
        }

        // 显示行程详情
        function showTripDetail(tripId) {
            showPage('tripDetailPage');
            // 这里可以根据tripId加载具体行程数据
        }

        // 显示添加菜单
        function showAddMenu() {
            const currentPageId = pageStack[pageStack.length - 1];
            if (currentPageId === 'vehiclePage') {
                alert('添加车辆');
            } else if (currentPageId === 'storyPage') {
                alert('添加故事记录');
            } else if (currentPageId === 'tripPage') {
                alert('添加行程');
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function () {
            updateNavBar();
        });
    </script>
</body>

</html>